<?php

namespace App;

use App\Emails\AdminRestartSubscriptionEmail;
use App\Emails\RestartSubscriptionEmail;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Lunar\Facades\Payments;
use MagicLink\Actions\LoginAction;
use MagicLink\MagicLink;


class Subscription extends Model
{
    use Traits\PaymentTrait;
    use HasFactory;
    use SoftDeletes;

    public $guarded = [];

    /* 
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    } */

    public static $combine_fields = [
        'id',
        'name',
        'address_line_1',
        'address_line_2',
        'city_state_zip',
        'building_door_code',
        'note',
    ];

    public static $fields = [
        'id',
        'name',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'building_door_code',
        'note',
    ];

    public $casts = [
        'start' => 'date',
        'meta' => 'array',
        'finish' => 'date',
        'pause_to' => 'date',
        'pause_from' => 'date',
        'is_gift' => 'boolean',
        'gift_fields' => 'json',
    ];

    public function incrementPublicationCount()
    {
        $this->publications_sent++;
        $this->save();
    }

    public function adjustPublicationCount($count)
    {
        $this->publications_sent = $count;
        $this->save();
    }

    public function DecreaseIssuesCount()
    {
        $this->issues--;
        $this->save();
    }

    public function adjustissuesCount($count)
    {
        $this->issues = $count;
        $this->save();
    }

    public function namedPublications()
    {
        return $this->hasMany(NamedPublication::class);
    }

    public function order()
    {
        return $this->hasOne(Order::class, 'subscription_id', 'id');
    }

    public function shipments()
    {
        return $this->hasOne(Shipment::class);
    }


    public function lunarOrderThrough()
    {
        return $this->hasOneThrough(
            \App\Lunar\Order::class,
            \App\Lunar\OrderLine::class,
            'meta->subscription->id',
            'id',
            'id',
            'order_id'
        );
    }
    public function lunar_order()
    {
        return $this->belongsTo(\App\Lunar\Order::class, 'order_id', 'id');
    }

    public function lunarByMeta()
    {
        return \App\Lunar\OrderLine::query()->where('meta->subscription->id', $this->id)->orderByDesc('created_at')->get();
    }

    public function getFrontEndAttribute()
    {
        $datePrint = [
            $this->start->format('M d, Y'), $this->finish->format('M d, Y')
        ];

        $result = [
            'id' => $this->id,
            'note' => $this->note,
            'start' => $this->start,
            'finish' => $this->finish,
            'date_print' => implode(' - ', $datePrint),
            'finish_print' => $this->finish->format('M d, Y'),
            'status' => $this->status(),
            'is_gift' => $this->is_gift,
            // 'canceled' => $this->canceled,
            'paused_to' => $this->pause_to,
            // 'is_paused' => $this->isPaused(),
            'paused_from' => $this->pause_from,
            'gift_fields' => $this->gift_fields,
            'payment_type' => $this->payment_type,
            'order' => $this->lunar_order,
            'orders' => $this->lunarByMeta()->map(function ($order) {
                return [
                    'date' => $order->created_at->format('M d, Y'),
                    'total' => $order->total->decimal(),
                ];
            }),
            'total' => $this->lunar_order?->total->decimal(),
            'free_gift' => optional($this->gift)->picture,
            //'percent' => $this->zone->percentByCycle($this->cycle),
            'credit_card' => optional($this->creditCard)->frontEnd,
            'renew' => $this->renew ? $this->finish->addDay() : null,
            'isRenew' => $this->issues === 0 && $this->canceled === 0 && $this->renew === 1,
            'isActive' => $this->finish->isAfter(today()) && $this->issues != 0 && $this->canceled === 0,
            'active' => $this->finish->isAfter(today()) && !$this->renew,
            'cycle' => optional($this->cycle)->only(['name', 'months']),
            'publications' => optional($this->cycle)->months,
            'gift_information' => $this->note ? ['note' => $this->note, 'name' => $this->customer->name,] : null,
            'delivery' => $this->only(['name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code']),
            /* 'upcoming' => [
                'start' => $this->zone->upcomingStartDate(),
                'finish' => $this->zone->upcomingStartDate()->addMonths($this->cycle->weeks),
            ], */
        ];

        Log::info('getFrontEndAttribute:', $result);
        return $result;
    }

    public function calculateTotalWithPromo($order)
    {
        $amount = floatval($order->amount);

        // Check if the order has a promo
        if (isset($order->promo)) {
            // Calculate the total based on the type of promo
            if ($order->promo['type'] === 'percent') {
                $discount = $amount * (floatval($order->promo['amount']) / 100);
                $total = $amount - $discount;
            } else {
                $total = $amount - floatval($order->promo['amount']);
            }
        } else {
            $total = $amount;
        }

        return $total;
    }

    public function status()
    {
        if ($this->canceled) {
            return 'Canceled';
        } else if ($this->isInActive()) {
            return 'No Issues Left';
        } else if ($this->isPaused()) {
            return 'Paused';
        } else if ($this->deleted_at) {
            return 'Deleted';
        }
        return 'Active';
    }

    public function isPaused()
    {
        return $this->pause_from < today() && $this->pause_to > today();
    }

    public function scopePaused($query)
    {
        return $query->where('pause_from', '<=', today())->where('pause_to', '>=', today());
    }

    public function scopeNotpaused($query)
    {
        return $query->where(function ($query) {
            $query->where(function ($query) {
                //the pause didnt start
                $query->whereNull('pause_from')
                    ->orWhere('pause_from', '>=', today());
            })->orWhere(function ($query) {
                //the pause finished
                $query->whereNull('pause_to')
                    ->orWhere('pause_to', '<=', today());
            });
        });
    }

    public function scopeActive($query)
    {
        return $query
            ->whereNull('canceled')
            ->orWhere('canceled', 0)
            ->orWhere('canceled', false);
    }

    public function scopeInActive($query)
    {
        return $query
            ->active()
            ->where('issues', 0)
            ->where('renew', false);
    }

    public function scopeExpired($query)
    {
        return $query
            ->active()
            ->where('issues', 0);
    }

    public function scopeCurrent($query)
    {
        return $query
            ->active()
            ->where([
                ['canceled', 0],
                ['issues', '>=', 1],
            ])->where(function ($query) {
                $query->where(function ($query) {
                    //the pause didnt start
                    $query->whereNull('pause_from')
                        ->orWhere('pause_from', '>=', today());
                })->orWhere(function ($query) {
                    //the pause finished
                    $query->whereNull('pause_to')
                        ->orWhere('pause_to', '<=', today());
                });
            });
    }

    public function extendFor($months, $reason)
    {

        $this->update([
            'issues' => $this->issues + $months,
        ]);

        $this->customer->messages()->create([
            'user_id' => auth()->user()->id,
            'content' => 'Extended subscribtion ID #'
                . $this->id . ' for ' . $months
                . ' months Reason: ' . $reason
        ]);
    }

    public static function forMonth($zone_id, $start, $end = null)
    {
        $start = now()->parse($start)->startOfMonth();
        $end = $end ? now()->parse($end)->endOfMonth() : $start->copy()->endOfMonth();

        return static::active()->notpaused()->where([
            ['start', '<=', $start],
            ['finish', '>=', $end]
        ])->where(function ($query) use ($zone_id) {
            return $zone_id ? $query->where('zone_id', $zone_id) : $query;
        })->get();
    }


    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class);
    }

    public function gift()
    {
        return $this->belongsTo(Gift::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function payment()
    {
        return $this->hasMany(Payment::class);
    }

    public function cycle()
    {
        return $this->belongsTo(Cycle::class);
    }

    public function zone()
    {
        return $this->belongsTo(Zone::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function subscriptionMonth()
    {
        return $this->belongsTo(SubscriptionMonth::class);
    }

    public function routeDetails()
    {
        return $this->hasMany(SubscriptionSubscriptionRoute::class);
    }

    public function toExcel()
    {
        $provider = optional($this->zone)->provider;

        switch (optional($provider)->combine) {
            case 'all':
                $data = [
                    'id' => $this->id,
                    'name' => $this->name,
                    'address' => "$this->address_line_1" . ($this->address_line_2 ? " $this->address_line_2" : "") . ", $this->city, $this->state $this->postal_code",
                    'note' => $this->created_at->isAfter(today()->subweeks(2)) ? $this->note : '',
                ];
                break;

            case 'city_state_zip':
                $data = [
                    'id' => $this->id,
                    'name' => $this->name,
                    'address_line_1' => $this->address_line_1,
                    'address_line_2' => $this->address_line_2,
                    'city_state_zip' => "$this->city, $this->state $this->postal_code",
                    'note' => $this->created_at->isAfter(today()->subweeks(2)) ? $this->note : '',
                ];
                break;
            default:
                $data = array_merge(
                    $this->only(self::$fields),
                    ['note' => $this->created_at->isAfter(today()->subweeks(2)) ? $this->note : ''],
                );
                break;
        }


        if (optional($provider)->include_phone) {
            $data['phone'] = optional($this->customer)->phone;
        }

        return $data;
    }

    public function pauseFor($start, $months)
    {
        $start = now()->parse($start);
        $this->update([
            'pause_from' => $start,
            'pause_to' => $start->copy()->addMonths($months),
            'finish' => $this->finish->addMonths($months),
        ]);
    }

    public function unPause()
    {
        $remaining = $this->pause_to->diffInDays(today());
        $this->update([
            'pause_from' => null,
            'pause_to' => null,
            'finish' => $this->finish->subDays($remaining),
        ]);
    }

    public function getExportFields()
    {
        return Gift::customFields()->map(function ($field) {
            return data_get($this->gift_fields, $field);
        });
    }

    public function changeCycle($cycle_id)
    {
        return;
        if ($this->cycle_id != $cycle_id) {
            $old_cycle = $this->cycle;
            $new_cycle = Cycle::find($cycle_id);

            $old_type = $old_cycle->months === 1 ? 'monthly' : 'yearly';
            $new_type = $new_cycle->months === 1 ? 'monthly' : 'yearly';
            $issues_left = $this->issues;
            // Update the subscription cycle
            $this->update([
                'cycle_id' => $cycle_id,
            ]);

            if ($this->credit_card) {
                $last_order = $this->orders()->latest()->first();
                // Fetch the promo if it exists
                $promo = Promo::find(data_get($last_order, 'promo_id'));

                // Get the price from the fixed prices table
                $old_price = Price::where('type', $old_type)->first()->amount;
                $new_price = Price::where('type', $new_type)->first()->amount;

                $issues_left = $this->issues;
                $adjusted_old_price = self::calculateRefundAmount($old_price, $issues_left);

                // Apply promo discounts if applicable
                if ($promo) {
                    $adjusted_old_price = $adjusted_old_price - ($adjusted_old_price * ($promo->discount / 100));
                    $new_price = $new_price - ($new_price * ($promo->discount / 100));
                }

                Log::info('old price' . $old_price);
                Log::info('new price' . $new_price);

                $diff = $new_price - $adjusted_old_price;

                // Create an adjustment order
                $order = Order::create([
                    'amount' => $diff,
                    'group_id' => $last_order->group_id . '_cycle_adjustment',
                    'customer_id' => $this->customer->id,
                    'subscription_id' => $this->id,
                ]);

                // Charge or refund based on the price difference
                if ($diff > 0) {
                    $credit_card_payment = $this->credit_card->charge($diff);
                    $this->issues = 11;
                    $this->save();
                } elseif ($diff < 0) {
                    $credit_card_payment = $this->credit_card->refund(-$diff);
                    $this->issues = 1;
                    $this->save();
                } else {
                    return; // diff is 0, don't do anything
                }

                // Record the payment
                Payment::create([
                    'amount' => $diff,
                    'order_id' => $order->id,
                    'credit_card_payment_id' => $credit_card_payment->id,
                    'credit_card_id' => optional($this->credit_card)->id,
                    'payment_type' => $this->payment_type,
                ]);
            }
        }
    }

    public static function calculateRefundAmount($old_price, $issues_left)
    {
        if ($issues_left > 1) {
            return ($old_price / 12) * $issues_left;
        }
        return $old_price;
    }


    public function canCancel()
    {
        return !$this->canceled
            && (optional($this->start)->isAfter(now()) || !optional($this->zone)->afterCutoff());
    }

    public function cancel($amount = null)
    {

        // cant canel after cutoff time
        if (
            !$this->canceled
            && ($this->start->isAfter(now()) || !$this->zone->afterCutoff())
        ) {
            if ($this->creditCard) {
                $amount = $amount ?? $this->lunarOrderThrough()->latest()->first()->total->value;
                $this->creditCard->refund($amount);
            }

            $this->update([
                'canceled' => true
            ]);

            app()->make(NotificationService::class)->subscriptionRenewalCanceling($this);
        }
    }

    public function restart()
    {
        $uuid = Str::random(32);


        $last_order = $this->orders()->latest()->first();
        $promo_id = data_get($last_order, 'promo_id');
        $price = $last_order->amount;
        $subscription = \App\Subscription::where('subscription_id', $last_order->subscription_id)->first();
        if ($last_order && $last_order->promo && $last_order->promo->work_on_renew) {
            // Calculate the price using the promo
            $promoPriceData = app('App\Http\Controllers\PromoController')->calculatePriceWithPromo(new Request([
                'subscription_type' => $subscription->subscription_type,
                'promo_code' => $last_order->promo->code
            ]));

            // Override the price
            if (!isset($promoPriceData->getData()->error)) {
                $price = $promoPriceData->getData()->Total;
            }
        }

        $credit_card_payment = null;

        if ($price > 0 && $this->payment_type == 'Credit Card') {

            try {
                // $credit_card_payment = CreditCardPayment::first();
                $credit_card_payment = $this->credit_card->charge($price);
            } catch (\Throwable $th) {

                $this->update([
                    'renew' => false,
                    'meta->failed_at' => now(),
                ]);

                app()->make(NotificationService::class)->subscriptionRenewalFailed($this);
                return;
            }
        }

        $order = Order::create([
            'amount' => $price,
            'group_id' => $uuid,
            'promo_id' => $promo_id,
            'customer_id' => $this->customer->id,
            'subscription_id' => $this->id,
        ]);

        if ($price > 0) {
            Payment::create([
                'amount' => $price,
                'order_id' => $order->id,
                'credit_card_payment_id' => optional($credit_card_payment)->id,
                'credit_card_id' => optional($this->credit_card)->id,
                'payment_type' => $this->payment_type,
            ]);
        }

        $start = $this->zone->upcomingStartDate();
        $start = !!$this->finish && $this->finish->isAfter(today()) ? now()->parse($this->finish) : $start;
        $this->finish = $start->copy()->addMonths($this->cycle->months - 1)->endOfMonth();

        $this->saveQuietly();

        $data = [
            'order' => $order,
            'year' => date('Y'),
            //'percent' => $percent,
            'price' => $price,
            'customer' => $this->customer,
            'id' => $this->id,
            'payment' => $order->payment,
            'gift' => $this->gift,
            'cycle' => $this->cycle,
            'subscription' => $this,
            'name' => explode(' ', $this->customer->name, 2)[0],
            'start' => $start->format('M j, Y'),
            'last_four' => data_get($this->credit_card, 'last_four'),
            'finish' => $this->finish->format('M j, Y'),
            'renew' => $this->renew ? $this->finish->copy()->format('M j, Y') : null,
            'picture' => data_get($this->gift, 'picture'),
            'payment_type' => Str::title(data_get($this->credit_card, 'type')),
            'creditCard' => optional($this->credit_card)->only('id', 'last_four', 'type'),
        ];

        (new RestartSubscriptionEmail)
            ->withData($data)
            ->withUser($this->customer)
            ->sendTo();

        (new AdminRestartSubscriptionEmail)
            ->withData($data)
            ->withUser(Customer::make(['email' => env('SubscriptionEmail')]))
            ->sendTo();
    }

    public function sendReminderEmail()
    {
        try {
            app()->make(NotificationService::class)->subscriptionExpired($this);

        } catch (\Throwable $th) {
            slack($th);
            Log::channel('slack')->info('Email renewal of Subscription #' . $this->id . ' failed');
        }
    }

    public function updateZoneBasedOnPostalCode()
    {
        $zone = Zone::getZip($this->postal_code);

        if ($zone) {
            $this->zone_id = $zone->id;
        } else {
            $this->zone_id = 0;

            Log::warning('Subscription with postal code ' . $this->postal_code . ' does not have an associated Zone.');
        }

        $this->saveQuietly();
    }


    public function addressString()
    {
        return $this->address_line_1 . ', ' . $this->city . ', ' . $this->state . ' ' . $this->postal_code;
    }

    public function setPromoIdAttribute()
    {
    }

    public function setChargePartiallyAttribute($value)
    {
    }

    public function isInActive()
    {
        return $this->issues == 0 && !$this->renew;
    }

    public function getMagicLink()
    {
        return MagicLink::create(
            new LoginAction(
                $this->customer,
                redirect("/account/subscriptions/$this->id"),
                'customers'
            ),
            24 * 7
        )->url;
    }

    public function getVerifiedAddress()
    {
        $array = array_map('trim', explode(',',
            $this->verified_address
                ? data_get($this, 'meta.address')
                : $this->addressString()
        ));

        return [
            'street1' => Str::of($array[0])->match('/(.*?)(?:(,|#|unit|apt|$))/')->jsonSerialize(),
            'street2' => $this->address_line_2
                ?? $this->apt_number
                    ?? Str::of($array[0])->match('/((?:,|#|unit|apt|$).*)/i')->jsonSerialize(),
            'city' => $this->city,
            'state' => $this->state,
            'postalCode' => $this->postal_code,
            'country' => $this->country ?? 'US',
        ];
    }

    public function createShipment($month, $mark_as_shipped = false): void
    {
        if ($this->isPausedForMonth($month)) {
            return;
        }

        $product = $month->createProduct();
        // $address = $this->lunar_order?->shippingAddress;

        // if($address == null) {
        //     Log::error('No address found for subscription ' . $this->id);
        //     return;
        // }

        $shipment = Shipment::firstOrCreate([
            'subscription_id' => $this->id,
            'subscription_month_id' => $month->id
        ], [
            'name' => $month->name,
            'purchasable_type' => $product->variant->getMorphClass(),
            'purchasable_id' => $product->variant->id,
            'address_id' => $this->lunar_order?->shippingAddress?->id,
            'zone_id' => $this->zone_id,
            'order_id' => $this->order_id,
            'line_one' => $this->address_line_1,
            'line_two' => $this->address_line_2,
            'city' => $this->city,
            'state' => $this->state,
            'postcode' => $this->postal_code,
            'shipped_at' => $mark_as_shipped ? now() : null,
        ]);


        if ($shipment->wasRecentlyCreated) {
            $this->incrementPublicationCount();
            $this->DecreaseIssuesCount();
        }
    }

    public function isPausedForMonth($month)
    {
        return !!in_array($month->id, data_get($this->meta, 'skip') ?? []);
    }

    protected function skip(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => data_get($this->meta, 'skip') ?? [],
            set: fn(mixed $value) => [
                'meta->skip' => array_map('intval', $value)
            ],
        );
    }

    public function resume($product = null, $free = false, $resource = 'auto-renew')
    {
        logToCsv('renew', "resuming #$this->id");
        logToCsv('renew', $this->toJson());

        $customer = $this->customer;

        $cart = Cart::create([]);

        if (!$product) {
            $cycleId = getCycleIds()[$this->subscription_type];
            $product = \App\Product::find($cycleId);
        }
        $variant = $product->variant;
        $cart->addOrUpdate($variant, 1);

        if($this->coupon_code) {
            $cart->update([
                'coupon_code' => $this->coupon_code
            ]);
        }

        $cart->setShippingAddress([
            'country_id' => 236,
            'title' => null,
            'first_name' => $customer->name,
            'last_name' => $customer->name,
            'city' => $this->city,
            'name' => $this->name,
            'state' => $this->state,
            'postcode' => $this->postal_code,
            'line_one' => $this->address_line_1,
            'line_two' => $this->address_line_2,
            'shipping_option' => 'basic',
        ]);

        $cart->setBillingAddress(
            $cart->shippingAddress
        );

        $cart->calculate();
        $order = $cart->createOrder();

        if($free) {
            $order->update([
                'status' => 'cash',
                'customer_id' => $customer->id,
            ]);
            $subscription_type = $product->name;
            $months = $subscription_type === 'yearly' ? 12 : 1;
            $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';

            $this->increment('issues', $months);

            $order->digitalLines->first()?->update([
                'meta->subscription' => $this
            ]);
            app()->make(NotificationService::class)->subscriptionRenewalConfirmation($order, $this);
            return response()->json(['success' => 'Free renewal'], 200);
        }

        $order->update([
            'meta' => [
                'creditCardData' => collect($this->creditCard)->merge(['creditCardId' => $this->creditCard->id]),
                'customer_id' => $customer->id,
                'payment_type' => 'credit'
            ],
            'customer_id' => $customer->id,
            'resource' => $resource
        ]);

        $driver = Payments::driver($this->creditCard->payment_type);
        $driver->order($order);

        if ($driver->charge()->success) {
            $order->update([
                'status' => 'payment-received'
            ]);
            $subscription_type = $product->name;
            $months = $subscription_type === 'yearly' ? 12 : 1;
            $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';

            $this->increment('issues', $months);

            $order->digitalLines->first()?->update([
                'meta->subscription' => $this
            ]);

            // $this->notificationService->sendNewOrderNotification($order, $this->subscription);

            return response()->json();

        } else {
            $this->update([
                'renew' => false,
                'meta->failed_at' => now(),
            ]);

            $order->update([
                'status' => 'payment-declined'
            ]);


            app()->make(NotificationService::class)->subscriptionRenewalFailed($this);

            return response()->json(['error' => 'Card Declined'], 403);
        }
    }

    public function _sendFailedEmail()
    {

    }
    function toExport() {
        return collect([
            'ID' => $this->id,
            'customer_name' => data_get($this->customer, 'name'),
            'customer_phone' => data_get($this->customer, 'phone'),
            'customer_email' => data_get($this->customer, 'email'),
        ])->merge([
            'Address' => $this->addressString()
        ]);
    }

}
